import { Meta } from '@/layout/Meta';
import { Container } from '@/ui/Container';
import { Card } from '@/ui/Card';
import { Link } from 'react-router-dom';
import { Download, FileText, Settings } from 'lucide-react';

const MetaIndexPage = () => {
  const utilities = [
    {
      id: 'logo',
      title: 'Logo Generator',
      description: 'Ringerike Landskap logoer i ulike formater og størrelser.',
      icon: Download,
      path: '/meta/logo',
      status: 'active',
      category: 'Design'
    },
    {
      id: 'arbeidskontrakt',
      title: 'Arbeidskontrakt Generator',
      description: 'Arbeidskontrakt i henhold til Arbeidsmiljøloven for Ringerike Landskap AS.',
      icon: FileText,
      path: '/meta/arbeidskontrakt',
      status: 'active',
      category: 'Formelt'
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-green-50 text-green-700">Aktiv</span>;
      case 'development':
        return <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-yellow-50 text-yellow-700">Under utvikling</span>;
      default:
        return <span className="inline-flex items-center px-2 py-1 rounded text-xs font-medium bg-gray-50 text-gray-700">Ukjent</span>;
    }
  };

  return (
    <>
      <Meta
        title="Meta - Ringerike Landskap"
        description="Digitale verktøy for Ringerike Landskap AS."
        keywords={['meta', 'utilities', 'verktøy', 'admin', 'Ringerike Landskap']}
      />
      <Container>
        <div className="max-w-6xl mx-auto py-12">
          {/* Header */}
          <div className="text-center mb-16">
            <div className="flex items-center justify-center mb-6">
              <div className="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center shadow-lg">
                <Settings className="h-8 w-8 text-white" />
              </div>
            </div>
            <h1 className="text-3xl sm:text-4xl font-bold text-gray-900 mb-4">
              Meta
            </h1>
            <p className="text-gray-600 text-lg max-w-2xl mx-auto leading-relaxed">
              Digitale verktøy for Ringerike Landskap AS.
            </p>
          </div>

          {/* Utilities Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-16">
            {utilities.map((utility) => {
              const IconComponent = utility.icon;

              return (
                <Link
                  key={utility.id}
                  to={utility.path}
                  className="group block"
                >
                  <div className="bg-white border border-gray-200 rounded-lg p-6 hover:border-gray-300 hover:shadow-md transition-all duration-200">
                    {/* Header with Icon and Status */}
                    <div className="flex items-center justify-between mb-4">
                      <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                        <IconComponent className="h-6 w-6 text-gray-700" />
                      </div>
                      {getStatusBadge(utility.status)}
                    </div>

                    {/* Content */}
                    <div className="mb-4">
                      <div className="text-xs text-gray-500 uppercase tracking-wide font-medium mb-1">
                        {utility.category}
                      </div>
                      <h3 className="text-lg font-semibold text-gray-900 mb-2">
                        {utility.title}
                      </h3>
                      <p className="text-gray-600 text-sm leading-relaxed">
                        {utility.description}
                      </p>
                    </div>

                    {/* Action */}
                    <div className="flex items-center justify-between pt-3 border-t border-gray-100">
                      <span className="text-sm font-medium text-gray-900">
                        {utility.status === 'active' ? 'Åpne verktøy' : 'Se detaljer'}
                      </span>
                      <ArrowRight className="h-4 w-4 text-gray-400 group-hover:text-gray-600 transition-colors" />
                    </div>
                  </div>
                </Link>
              );
            })}
          </div>


        </div>
      </Container>
    </>
  );
};

export default MetaIndexPage;
