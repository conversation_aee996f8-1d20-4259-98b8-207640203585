# Unified Codebase Architecture

## Overview

This document defines the streamlined, unified architecture of the Ringerike Landskap website after systematic consolidation and bloat removal.

## Core Architecture Principles

1. **Single Responsibility**: Each component serves one clear purpose
2. **Architectural Isolation**: Meta utilities isolated from main site
3. **Centralized Utilities**: All shared logic consolidated in `src/lib/`
4. **Consistent Structure**: Uniform patterns across all components

## Directory Structure

```
src/
├── app/                    # Application entry point
├── components/             # Specialized cross-cutting components
│   ├── Meta/              # Meta utilities (isolated)
│   └── SEO/               # SEO components
├── content/               # Dynamic content data
├── data/                  # Static application data
├── layout/                # Layout components (Header, Footer, Meta)
├── lib/                   # Core utilities and business logic
│   ├── api/              # API layer
│   ├── config/           # Configuration
│   ├── constants/        # Application constants
│   ├── context/          # React contexts
│   ├── hooks/            # Custom React hooks
│   ├── meta/             # Meta utilities (isolated)
│   ├── types/            # TypeScript type definitions
│   └── utils/            # Utility functions
├── pages/                 # Page-level components
├── sections/              # Section components (10-home, 20-about, etc.)
├── styles/               # Global styles
└── ui/                   # Reusable UI components
```

## Component Categories

### Core Requirements (Indispensable)

1. **Business Logic Components**
   - Arbeidskontrakt generator (`src/components/Meta/`)
   - Contact form (`src/sections/60-contact/`)
   - Service/project displays (`src/sections/30-services/`, `src/sections/40-projects/`)

2. **UI Foundation**
   - Button, Card, Container (`src/ui/`)
   - Form components (`src/ui/Form/`)
   - Layout components (`src/layout/`)

3. **Content Management**
   - Services data (`src/data/services.ts`)
   - Projects data (`src/data/projects.ts`)
   - Content system (`src/content/`)

4. **Utility Layer**
   - Validation utilities (`src/lib/utils/validation.ts`)
   - Formatting utilities (`src/lib/utils/formatting.ts`)
   - Filtering system (`src/lib/utils/filtering.ts`)

### Architectural Patterns

#### 1. Meta Utilities Isolation
```typescript
// Isolated routing
<Route path="/meta/*" element={<MetaRouter />} />

// Error boundaries prevent main site impact
<MetaErrorBoundary>
  <ArbeidskontraktPage />
</MetaErrorBoundary>
```

#### 2. Centralized Validation
```typescript
// Single source of truth for validation
import { validateEmail, validatePhone, validateRequired } from '@/lib/utils/validation';
```

#### 3. Unified Date Formatting
```typescript
// Consolidated date formatting functions
import { formatNorwegianContractDate, formatNorwegianDateTime } from '@/lib/utils/formatting';
```

## Consistency Protocol

### Import Conventions
```typescript
// 1. External dependencies
import { useState } from 'react';

// 2. UI components (barrel imports)
import { Card, Button } from '@/ui';

// 3. Specific utilities
import { validateEmail } from '@/lib/utils/validation';

// 4. Local components (relative)
import LocalComponent from './LocalComponent';
```

### Component Structure
```typescript
// Standard component pattern
const ComponentName = () => {
  // State and hooks
  // Event handlers
  // Render logic
};

export default ComponentName;
```

### File Naming
- Components: PascalCase (`ComponentName.tsx`)
- Utilities: camelCase (`utilityName.ts`)
- Constants: UPPER_SNAKE_CASE (`CONSTANT_NAME`)

## Eliminated Redundancies

### Removed Components
- ❌ `ImageTester.tsx` - Development-only test component
- ❌ `src/components/Meta/index.tsx` - Redundant meta component
- ❌ `src/docs/` - Duplicate documentation
- ❌ `scripts/verify-seasonal.js` - Duplicate script

### Consolidated Logic
- ✅ Validation logic centralized in `src/lib/utils/validation.ts`
- ✅ Date formatting unified in `src/lib/utils/formatting.ts`
- ✅ Single source of truth for all utilities

## Quality Metrics

- **Bundle Size**: Reduced main bundle from 187.34 kB to 185.46 kB
- **Module Count**: Reduced from 1761 to 1760 modules
- **Code Duplication**: Eliminated 44+ lines of duplicate code
- **Maintainability**: Single source of truth for all shared logic

## Future Development Guidelines

1. **Before Adding Components**: Check if existing components can be extended
2. **Validation**: Always use centralized validation utilities
3. **Date Formatting**: Use unified formatting functions
4. **Meta Utilities**: Maintain architectural isolation
5. **Documentation**: Update this document for structural changes

This architecture ensures maximum maintainability while preserving all business functionality.
