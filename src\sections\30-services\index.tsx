import { useState, useMemo, useEffect } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import Hero from "@/ui/Hero";
import { MapPin, Shield, Droplet, Filter, Calendar } from "lucide-react";
import Container from "@/ui/Container";
import Button from "@/ui/Button";
import { normalizeString } from "@/lib/utils/strings";
import { encodeImagePath } from "@/lib/utils/paths";
import { Meta } from "@/layout/Meta";

import { useData, useSeasonalData } from "@/lib/hooks";
import { getServices } from "@/lib/api";

import {
  filterServices,
  getUniqueFeatures,
  serviceMatchesCategory,
  serviceHasFeature,
  getServiceMainCategory
} from "@/lib/utils/filtering";
import { SEASONAL_SERVICES } from "@/lib/constants/seasonal";
import {
  getServiceCategoriesForSeason,
  getSeasonsForServiceCategory
} from "@/lib/constants/service-mappings";
import { SeasonType } from "@/lib/types";
import { PRIMARY_AREA } from "@/lib/constants/locations";



const ServicesPage = () => {
  const location = useLocation();
  const navigate = useNavigate();

  // Use our new seasonal hook to get seasonal data
  const { currentSeason, currentSeasonDisplay } = useSeasonalData();
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedFeature, setSelectedFeature] = useState<string | null>(null);
  const [selectedSeason, setSelectedSeason] = useState<string | null>(null);

  // Function to update URL with current filter state
  const updateUrlWithFilters = (category: string | null, _feature: string | null, season: string | null) => {
    const params = new URLSearchParams();

    if (season) {
      params.set('sesong', season);
    }

    if (category) {
      params.set('kategori', category);
    }

    // Feature filter is hidden but we keep the state management for future reimplementation
    // if (_feature) {
    //   params.set('funksjon', _feature);
    // }

    const queryString = params.toString();
    const newUrl = queryString ? `${location.pathname}?${queryString}` : location.pathname;

    // Use replace instead of push to avoid creating new history entries when filters change
    navigate(newUrl, { replace: true });
  };

  // Read URL parameters and set initial filter values
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const seasonParam = searchParams.get('sesong');

    if (seasonParam) {
      setSelectedSeason(seasonParam);
    }

    const categoryParam = searchParams.get('kategori');
    if (categoryParam) {
      setSelectedCategory(categoryParam);
    }

    // Still read the feature parameter for backward compatibility
    // but it won't be shown in the UI
    const featureParam = searchParams.get('funksjon');
    if (featureParam) {
      setSelectedFeature(featureParam);
    }
  }, [location.search]);

  // Use the useData hook to fetch services
  const { data: services, loading: servicesLoading } = useData(getServices, []);

  // Use the data from the API, or an empty array if still loading
  const servicesData = services || [];

  // Get unique service categories based on the selected season
  const categories = useMemo(() => {
    // If a season is selected, only show categories from that season's mapping
    if (selectedSeason) {
      // Use our new consolidated mapping to get categories for this season
      return getServiceCategoriesForSeason(selectedSeason as SeasonType);
    }

    // If no season is selected, show all unique categories from all services
    const categoriesSet = new Set(
      servicesData.map(service => getServiceMainCategory(service))
    );

    // Ensure we have categories even if servicesData is empty
    const result = Array.from(categoriesSet);

    // If we still don't have categories, use all possible categories from our mapping
    if (result.length === 0) {
      const allCategories = new Set<string>();
      Object.values(SEASONAL_SERVICES).forEach(season => {
        season.categories.forEach(category => allCategories.add(category));
      });
      return Array.from(allCategories);
    }

    return result;
  }, [selectedSeason, servicesData]);

  // Get seasons that are relevant for the selected category
  const relevantSeasons = useMemo(() => {
    if (!selectedCategory) {
      // If no category is selected, all seasons are relevant
      return Object.keys(SEASONAL_SERVICES) as SeasonType[];
    }

    // Use our new consolidated mapping to get seasons for this category
    // First try to get seasons directly from our mapping
    const seasonsFromMapping = getSeasonsForServiceCategory(selectedCategory);
    if (seasonsFromMapping.length > 0) {
      return seasonsFromMapping;
    }

    // If not found in our mapping, fall back to the old method
    // Find the service that matches the selected category
    const matchingService = servicesData.find(service =>
      serviceMatchesCategory(service, selectedCategory)
    );

    if (!matchingService) {
      return [];
    }

    // Find which seasons include this category or have matching features
    return Object.entries(SEASONAL_SERVICES)
      .filter(([_, seasonData]) => {
        // Check if the season includes this category
        const categoryMatch = seasonData.categories.some(category =>
          normalizeString(category) === normalizeString(selectedCategory) ||
          normalizeString(category).includes(normalizeString(selectedCategory)) ||
          normalizeString(selectedCategory).includes(normalizeString(category))
        );

        if (categoryMatch) {
          return true;
        }

        // Check if the service has features that match the season's features
        const featureMatch = matchingService.features?.some(serviceFeature =>
          seasonData.features.some(seasonFeature =>
            normalizeString(serviceFeature).includes(normalizeString(seasonFeature)) ||
            normalizeString(seasonFeature).includes(normalizeString(serviceFeature))
          )
        ) || false;

        return featureMatch;
      })
      .map(([season, _]) => season);
  }, [selectedCategory, servicesData]);

  // Get unique features only from services that match the selected season and category
  const features = useMemo(() => {
    // Use the centralized utility function
    const uniqueFeatures = getUniqueFeatures(servicesData, selectedSeason, selectedCategory);

    // If we don't have any features, get all possible features from all services
    if (uniqueFeatures.length === 0) {
      // First try to get features from all services
      const allFeaturesFromServices = new Set(
        servicesData.flatMap(service => service.features || [])
      );

      // If we still don't have features, use all possible features from SEASONAL_SERVICES
      if (allFeaturesFromServices.size === 0) {
        const allFeatures = new Set<string>();
        Object.values(SEASONAL_SERVICES).forEach(season => {
          season.features.forEach(feature => allFeatures.add(feature));
        });
        return Array.from(allFeatures);
      }

      return Array.from(allFeaturesFromServices);
    }

    return uniqueFeatures;
  }, [selectedCategory, selectedSeason, servicesData]);

  // Filter services based on selected filters
  const filteredServices = useMemo(() => {
    // Use the centralized filtering utility
    return filterServices(servicesData, selectedCategory, selectedFeature, selectedSeason);
  }, [selectedCategory, selectedFeature, selectedSeason, servicesData]);

  // Reset all filters
  const resetFilters = () => {
    setSelectedCategory(null);
    setSelectedFeature(null);
    setSelectedSeason(null);

    // Update URL to remove all filter parameters
    updateUrlWithFilters(null, null, null);
  };

  // Update URL when filters change
  useEffect(() => {
    // Skip the initial render
    if (location.search && !selectedSeason && !selectedCategory && !selectedFeature) {
      return;
    }

    updateUrlWithFilters(selectedCategory, selectedFeature, selectedSeason);
  }, [selectedCategory, selectedFeature, selectedSeason]);

  const benefits = [
    {
      icon: <MapPin className="w-6 h-6 text-green-500" />,
      title: "Lokalkunnskap",
      description:
        `Med base i Røyse kjenner vi Ringerikes unike terreng og jordforhold. Vi graver, bygger og planter med et våkent blikk for naturens flyt og hver eiendoms egenart.`,
    },
    {
      icon: <Shield className="w-6 h-6 text-green-500" />,
      title: "Tilpassede løsninger",
      description:
        "Hvert prosjekt er unikt. Vi skreddersyr løsninger etter kundens behov, terrengets beskaffenhet og lokale klimaforhold, med fokus på kvalitet, detaljer og ærlig kommunikasjon.",
    },
    {
      icon: <Droplet className="w-6 h-6 text-green-500" />,
      title: "Klimatilpasset",
      description:
        `Alle løsninger er spesialtilpasset for å takle både tørre somre og våte høstdager. Våre løsninger springer ut av en tett dialog med landskapet – solide, men likevel myke i uttrykket.`,
    },
  ];

  // Add loading state for the services grid
  if (servicesLoading) {
    return (
      <div>
        <Hero
          title={`Anleggsgartnertjenester i ${PRIMARY_AREA.name}`}
          subtitle="Vi tilbyr alt fra legging av ferdigplen til bygging av støttemurer, tilpasset både private hager og næringseiendommer. Med inngående kjennskap til områdets klima og terreng, leverer vi løsninger som varer i generasjoner."
          backgroundImage="/images/hero/hero-testimonials-cortensteel.webp"
        />
        <Container className="py-8 sm:py-12">
          <div className="space-y-12 mb-16">
            {[...Array(4)].map((_, index) => (
              <div key={index} className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                <div className="h-[280px] bg-gray-200 animate-pulse rounded-lg"></div>
                <div className="space-y-4">
                  <div className="h-8 bg-gray-200 animate-pulse rounded w-3/4"></div>
                  <div className="h-20 bg-gray-200 animate-pulse rounded"></div>
                  <div className="space-y-2">
                    {[...Array(3)].map((_, i) => (
                      <div key={i} className="h-4 bg-gray-200 animate-pulse rounded w-5/6"></div>
                    ))}
                  </div>
                  <div className="h-10 bg-gray-200 animate-pulse rounded w-1/3"></div>
                </div>
              </div>
            ))}
          </div>
        </Container>
      </div>
    );
  }

  return (
    <>
      <Meta
        title="Anleggsgartnertjenester"
        description={`Vi tilbyr profesjonelle anleggsgartnertjenester i ${PRIMARY_AREA.name}. Fra belegningsstein og støttemurer til ferdigplen og kantstein - vi leverer kvalitetsarbeid tilpasset lokale forhold.`}
        keywords={[
          "anleggsgartner",
          "belegningsstein",
          "støttemurer",
          "ferdigplen",
          "kantstein",
          "anleggsgartnertjenester",
          PRIMARY_AREA.name
        ]}
      />
      <div itemScope itemType="http://schema.org/Service">
        <Hero
        title={`Anleggsgartnertjenester i ${PRIMARY_AREA.name}`}
        subtitle="Vi tilbyr alt fra legging av ferdigplen til bygging av støttemurer, tilpasset både private hager og næringseiendommer. Med inngående kjennskap til områdets klima og terreng, leverer vi løsninger som varer i generasjoner."
        backgroundImage="/images/hero/hero-testimonials-cortensteel.webp"
      />

      <Container className="py-8 sm:py-12">
        {/* Filters */}
        <div className="mb-8 p-6 bg-gray-50 rounded-lg">
          <div className="flex items-center gap-2 mb-4 text-gray-700 font-medium">
            <Filter className="w-5 h-5" />
            <h2 className="text-xl font-semibold">Filtrer tjenester</h2>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {/* Season filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                <div className="flex items-center gap-1.5">
                  <Calendar className="w-4 h-4" />
                  <span>Sesong</span>
                </div>
              </label>
              <select
                value={selectedSeason || ""}
                onChange={(e) => {
                  const newSeason = e.target.value || null;
                  setSelectedSeason(newSeason);
                  // Reset category and feature when season changes to avoid inconsistent filtering
                  setSelectedCategory(null);
                  setSelectedFeature(null);
                }}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                disabled={relevantSeasons.length === 0}
              >
                <option value="">
                  Alle sesonger
                </option>
                {relevantSeasons.map((season) => (
                  <option key={season} value={season}>
                    {season.charAt(0).toUpperCase() + season.slice(1)}
                  </option>
                ))}
              </select>
            </div>

            {/* Category filter */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Kategori
              </label>
              <select
                value={selectedCategory || ""}
                onChange={(e) => {
                  const newCategory = e.target.value || null;
                  setSelectedCategory(newCategory);

                  // If a category is selected and it's only relevant for one season,
                  // automatically select that season
                  if (newCategory) {
                    // Get seasons for this category from our consolidated mapping
                    const seasons = getSeasonsForServiceCategory(newCategory);

                    // If there's only one relevant season, select it
                    if (seasons.length === 1) {
                      setSelectedSeason(seasons[0]);
                    }

                    // Check if the selected category is compatible with the current function
                    // If not, reset function to "Alle funksjoner"
                    if (selectedFeature) {
                      // Get services that match both the new category and the current feature
                      const matchingServices = services?.filter(service =>
                        serviceMatchesCategory(service, newCategory) &&
                        serviceHasFeature(service, selectedFeature)
                      );

                      // If no services match both filters, reset the feature filter
                      if (!matchingServices || matchingServices.length === 0) {
                        setSelectedFeature(null);
                      }
                    }
                  }
                }}
                className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
                disabled={servicesLoading || categories.length === 0}
              >
                <option value="">
                  Alle kategorier
                </option>
                {categories.map((category) => (
                  <option key={category} value={category}>
                    {category}
                  </option>
                ))}
              </select>
            </div>

            {/* Hidden Feature filter - kept for future reimplementation */}
            <div className="hidden">
              <select
                value={selectedFeature || ""}
                onChange={(e) => {
                  const newFeature = e.target.value || null;
                  setSelectedFeature(newFeature);
                }}
              >
                <option value="">Alle funksjoner</option>
                {features.map((feature) => (
                  <option key={feature} value={feature}>{feature}</option>
                ))}
              </select>
            </div>

            {/* Reset button */}
            <div className="md:col-span-2">
              <button
                onClick={resetFilters}
                className="w-full px-4 py-2 bg-gray-200 text-gray-800 rounded-md hover:bg-gray-300 transition-colors"
              >
                Nullstill filtre
              </button>
            </div>
          </div>

          {/* Current season suggestion */}
          {!selectedSeason && (
            <div className="mt-4 p-3 bg-green-50 border-l-4 border-green-500 rounded-r-md">
              <div className="flex items-center gap-2">
                <Calendar className="w-5 h-5 text-green-600" />
                <p className="text-sm text-gray-700">
                  <span className="font-medium">Tips:</span> {currentSeasonDisplay.toLowerCase()} er en god tid for{' '}
                  {SEASONAL_SERVICES[currentSeason].categories.join(', ').toLowerCase()}.{' '}
                  <button
                    onClick={() => setSelectedSeason(currentSeason)}
                    className="text-green-600 hover:underline font-medium"
                  >
                    Vis tjenester for {currentSeason}en
                  </button>
                </p>
              </div>
            </div>
          )}
        </div>

        {/* Services grid */}
        <div className="space-y-12 mb-16">
          {filteredServices.length > 0 ? (
            filteredServices.map((service, index) => (
              <div
                key={service.id}
                className={`grid grid-cols-1 lg:grid-cols-2 gap-8 items-center ${
                  index % 2 === 0 ? "" : "lg:flex-row-reverse"
                }`}
                itemProp="hasOfferCatalog"
              >
                <div className="relative h-[200px] sm:h-[280px] rounded-lg overflow-hidden">
                  <img
                    src={encodeImagePath(service.image)}
                    alt={service.title}
                    className="absolute inset-0 w-full h-full object-cover"
                    itemProp="image"
                    loading="lazy"
                  />
                </div>
                <div className="space-y-4">
                  <h2
                    className="text-xl sm:text-2xl font-semibold"
                    itemProp="name"
                  >
                    {service.title}
                  </h2>
                  <p
                    className="text-gray-700 leading-relaxed"
                    itemProp="description"
                  >
                    {service.longDescription ||
                      service.description}
                  </p>
                  {service.features && (
                    <ul className="space-y-2">
                      {service.features.map(
                        (feature, index) => (
                          <li
                            key={index}
                            className="flex items-center gap-2 text-gray-600 text-sm"
                          >
                            <span className="w-1.5 h-1.5 bg-green-500 rounded-full" />
                            {feature}
                          </li>
                        )
                      )}
                    </ul>
                  )}
                  <Link
                    to={`/tjenester/${service.id}`}
                    className="inline-block bg-green-500 text-white px-6 py-2 rounded-md hover:bg-green-600 transition-colors mt-4"
                  >
                    Les mer om {service.title.toLowerCase()}
                  </Link>
                </div>
              </div>
            ))
          ) : (
            <div className="text-center py-12">
              <h3 className="text-xl font-semibold mb-2">
                Ingen tjenester funnet
              </h3>
              <p className="text-gray-600 mb-4">
                Prøv å endre filtrene eller{" "}
                <button
                  onClick={resetFilters}
                  className="text-green-600 hover:underline"
                >
                  vis alle tjenester
                </button>
              </p>
            </div>
          )}
        </div>

        {/* Benefits section - MOVED BELOW SERVICES */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12">
          {benefits.map((benefit, index) => (
            <div
              key={index}
              className="text-center p-6 bg-white rounded-lg shadow-sm"
            >
              <div className="inline-block p-3 bg-green-50 rounded-full mb-4">
                {benefit.icon}
              </div>
              <h3 className="text-lg font-semibold mb-2">
                {benefit.title}
              </h3>
              <p className="text-sm text-gray-600">
                {benefit.description}
              </p>
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <div className="mt-12 bg-gray-50 rounded-lg p-8 text-center">
          <h2 className="text-xl sm:text-2xl font-semibold mb-3">
            Usikker på hvilken løsning som passer best?
          </h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto text-sm">
            Med inngående kjennskap til {PRIMARY_AREA.name}-området hjelper vi
            deg å finne den beste løsningen for ditt uterom. Vi
            kommer gjerne på gratis befaring for å se på
            mulighetene.
          </p>
          <Button
            to="/kontakt"
            variant="primary"
            className="px-8 py-3"
          >
            Book gratis befaring
          </Button>
        </div>
      </Container>
      </div>
    </>
  );
};

export default ServicesPage;