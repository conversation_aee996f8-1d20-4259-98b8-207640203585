import { IMAGE_PATHS, IMAGE_CATEGORIES } from '@/lib/config/images';

// Re-export IMAGE_CATEGORIES for use in other modules
export { IMAGE_CATEGORIES };

/**
 * Constants and utilities for working with images throughout the application
 */

export interface ImageFile {
  filename: string;
  path: string;
  category: string;
  metadata?: {
    title?: string;
    description?: string;
    location?: string;
    date?: string;
  };
}

/**
 * Fallback image lists for each category
 * Centralized to avoid duplication across functions
 */
const FALLBACK_IMAGES: Record<string, string[]> = {
  belegg: [
    'IMG_0035_59.923192_10.649406.webp',
    'IMG_0085_60.163047_10.252728.webp',
    'IMG_0121_60.169094_10.276781.webp',
    'IMG_0129_60.169103_10.276797.webp',
    'IMG_0208_59.932006_10.478197.webp',
    'IMG_0451_60.274486_10.186981.webp',
    'IMG_0453_60.274647_10.187028.webp',
    'IMG_0715.webp',
    'IMG_0717.webp',
    'IMG_1935_59.912947_10.575681.webp',
    'IMG_2941_60.181500_10.273881.webp',
    'IMG_3001_60.181519_10.274283.webp'
  ],
  hekk: [
    'hekk_20.webp', 'IMG_0167.webp', 'IMG_1841.webp', 'IMG_2370.webp',
    'IMG_2371.webp', 'IMG_3077.webp'
  ],
  stål: [
    'IMG_0001.webp', 'IMG_0002.webp', 'IMG_0003.webp', 'IMG_0004.webp',
    'IMG_0005.webp', 'IMG_0006.webp', 'IMG_0007.webp', 'IMG_0008.webp',
    'IMG_0009.webp', 'IMG_0010.webp'
  ],
  støttemur: [
    'IMG_0001.webp', 'IMG_0002.webp', 'IMG_0003.webp', 'IMG_0004.webp',
    'IMG_0005.webp', 'IMG_0006.webp', 'IMG_0007.webp', 'IMG_0008.webp'
  ],
  'trapp-repo': [
    'IMG_0001.webp', 'IMG_0002.webp', 'IMG_0003.webp', 'IMG_0004.webp',
    'IMG_0005.webp', 'IMG_0006.webp', 'IMG_0007.webp', 'IMG_0008.webp'
  ],
  kantstein: [
    'IMG_0001.webp', 'IMG_0002.webp', 'IMG_0003.webp', 'IMG_0004.webp',
    'IMG_0005.webp', 'IMG_0006.webp', 'IMG_0007.webp', 'IMG_0008.webp'
  ],
  ferdigplen: [
    'IMG_0001.webp', 'IMG_0002.webp', 'IMG_0003.webp', 'IMG_0004.webp',
    'IMG_0005.webp', 'IMG_0006.webp', 'IMG_0007.webp', 'IMG_0008.webp'
  ],
  platting: [
    'IMG_3251.webp', 'IMG_4188.webp'
  ]
};

/**
 * Converts an image path to its WebP version
 *
 * @param imagePath The original image path
 * @returns The WebP version of the image path
 */
export const getWebPVersion = (imagePath: string): string => {
  // If already a WebP image, return as is
  if (imagePath.endsWith(".webp")) {
    return imagePath;
  }

  // For external URLs (like Unsplash), return original
  if (imagePath.startsWith("http")) {
    return imagePath;
  }

  // Create WebP path by replacing extension
  return imagePath.replace(/\.(jpe?g|png|gif)$/i, ".webp");
};

/**
 * Interface for image with geocoordinates
 */
export interface GeoImage extends ImageFile {
  coordinates?: {
    latitude: number;
    longitude: number;
  };
}

/**
 * Extracts geocoordinates from a filename if present
 *
 * @param filename The filename that may contain geocoordinates
 * @returns An object with the base filename and coordinates if found
 */
export const extractGeoCoordinates = (filename: string): {
  baseFilename: string;
  coordinates?: { latitude: number; longitude: number }
} => {
  // Match pattern like IMG_0035_59.923192_10.649406.webp
  const geoPattern = /^(.+)_(\d+\.\d+)_(\d+\.\d+)\.(\w+)$/;
  const match = filename.match(geoPattern);

  if (match) {
    const [, baseFilename, latitude, longitude, extension] = match;
    return {
      baseFilename: `${baseFilename}.${extension}`,
      coordinates: {
        latitude: parseFloat(latitude),
        longitude: parseFloat(longitude)
      }
    };
  }

  return { baseFilename: filename };
};

/**
 * Gets all images from a specific category, handling geocoordinate filenames
 *
 * @param category The category to get images from
 * @returns An array of image files
 */
export const getImagesFromCategory = (category: keyof typeof IMAGE_CATEGORIES): GeoImage[] => {
  const categoryPath = IMAGE_PATHS.categories[category];

  // Use the centralized fallback images
  // Use the centralized fallback images
  const images = FALLBACK_IMAGES[category] || [];

  return images.map(filename => {
    // Extract geocoordinates if present
    const { coordinates } = extractGeoCoordinates(filename);

    // Create the image object
    const imageObject: GeoImage = {
      filename,
      path: `${categoryPath}/${filename}`,
      category: IMAGE_CATEGORIES[category],
      metadata: {
        title: `${IMAGE_CATEGORIES[category]} prosjekt`,
        description: `Profesjonell utførelse av ${IMAGE_CATEGORIES[category].toLowerCase()}`
      }
    };

    // Add coordinates if available
    if (coordinates) {
      imageObject.coordinates = coordinates;

      // Add location to metadata if coordinates are available
      if (imageObject.metadata) {
        imageObject.metadata.location = `${coordinates.latitude.toFixed(6)}, ${coordinates.longitude.toFixed(6)}`;
      }
    }

    return imageObject;
  });
};

/**
 * Gets the optimal image size for a given viewport width
 *
 * @param viewportWidth The current viewport width
 * @returns The optimal image size
 */
export const getOptimalImageSize = (viewportWidth: number): string => {
  if (viewportWidth < 640) return 'sm';
  if (viewportWidth < 1024) return 'md';
  if (viewportWidth < 1536) return 'lg';
  return 'xl';
};

/**
 * Finds the correct image filename in a category directory, handling geocoordinate filenames
 *
 * @param category The category directory (e.g., 'belegg', 'stål')
 * @param baseFilename The base filename without geocoordinates (e.g., 'IMG_3037')
 * @returns The full image path with correct filename
 */
export const getImagePathWithFallback = (category: string, baseFilename: string): string => {
  // Extract the base filename without extension
  const baseNameWithoutExt = baseFilename.replace(/\.\w+$/, '');

  // Find the matching file in the centralized fallback images
  const matchingFile = FALLBACK_IMAGES[category]?.find((filename: string) =>
    filename.startsWith(baseNameWithoutExt + '_') || filename === baseFilename
  );

  // If a matching file is found, use it; otherwise, use the original filename
  const finalFilename = matchingFile || baseFilename;

  // Return the full path
  return `/images/categorized/${category}/${finalFilename}`;
};