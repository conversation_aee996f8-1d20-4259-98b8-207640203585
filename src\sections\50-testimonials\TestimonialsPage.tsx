
import React from "react";
import { <PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import { <PERSON> } from "react-router-dom";
import Container from "@/ui/Container";
import { Loading } from "@/ui";
import { useData } from "@/lib/hooks";
import { getTestimonials } from "@/lib/api";
import { TestimonialType } from "@/lib/types";
import { getPageSeo } from "@/lib/constants/seo";
import { Meta } from "@/layout/Meta";
import TestimonialsSchema from "./TestimonialsSchema";
import Testimonial from "./Testimonial";

// Simple average rating component
const AverageRating = ({ testimonials }: { testimonials: TestimonialType[] }) => {
  if (!testimonials.length) return null;

  const totalRating = testimonials.reduce(
    (sum, testimonial) => sum + testimonial.rating,
    0
  );
  const averageRating = totalRating / testimonials.length;
  const roundedRating = Math.round(averageRating * 10) / 10;

  return (
    <div className="flex items-center">
      <div className="flex items-center mr-2">
        {[...Array(5)].map((_, i) => (
          <Star
            key={i}
            className={`w-5 h-5 ${
              i < Math.floor(roundedRating)
                ? "text-yellow-400 fill-current"
                : i < roundedRating
                ? "text-yellow-400 fill-current opacity-50"
                : "text-gray-300"
            }`}
          />
        ))}
      </div>
      <span className="font-medium">{roundedRating.toFixed(1)}</span>
      <span className="text-gray-500 text-sm ml-2">
        ({testimonials.length} {testimonials.length === 1 ? "vurdering" : "vurderinger"})
      </span>
    </div>
  );
};

const TestimonialsPage = () => {
  // Use the useData hook to fetch testimonials
  const { data: testimonials = [], loading } = useData(getTestimonials, []);

  // Get centralized SEO data for testimonials page
  const seoData = getPageSeo('testimonials');

  // Show loading state while data is being fetched
  if (loading) {
    return <Loading message="Laster kundehistorier..." fullScreen />;
  }

  return (
    <>
      <Meta
        title={seoData.title}
        description={seoData.description}
        keywords={seoData.keywords}
        schema={seoData.schema}
      />
      <section className="bg-gray-50 py-16">
      {testimonials && <TestimonialsSchema testimonials={testimonials} />}
      <Container>
        <div className="text-center mb-12">
          <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
            Hva våre kunder sier
          </h1>
          <div className="flex items-center justify-center gap-2 text-green-600 mb-4">
            <Sparkles className="h-5 w-5" />
            <span className="font-medium">
              Tilbakemeldinger fra fornøyde kunder
            </span>
          </div>
          <div className="flex justify-center mb-4">
            {testimonials && <AverageRating testimonials={testimonials} />}
          </div>
          <p className="max-w-2xl mx-auto text-gray-600">
            Vi er stolte av arbeidet vårt og verdsetter
            tilbakemeldingene fra våre kunder. Her er noen av
            historiene fra folk som har opplevd forskjellen med
            Ringerike Landskap.
          </p>
        </div>

        {/* Testimonials grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {testimonials && testimonials.map((testimonial, index) => (
            <div key={index} className="bg-white shadow rounded-lg overflow-hidden">
              <Testimonial testimonial={testimonial} variant="default" />
            </div>
          ))}
        </div>

        {/* Call to action */}
        <div className="mt-16 text-center">
          <h2 className="text-2xl font-bold mb-4">
            Klar for å skape din drømmehage?
          </h2>
          <p className="text-gray-600 mb-6 max-w-2xl mx-auto">
            La oss hjelpe deg med å transformere ditt uterom til et
            vakkert og funksjonelt område som du og din familie kan
            nyte i mange år fremover.
          </p>
          <Link
            to="/kontakt"
            className="inline-block bg-green-600 hover:bg-green-700 text-white font-medium py-3 px-8 rounded-md transition-colors"
          >
            Kontakt oss i dag
          </Link>
        </div>
      </Container>
      </section>
    </>
  );
};

// Simple TestimonialsSection component for the homepage
export const TestimonialsSection = ({ testimonials }: { testimonials: TestimonialType[] }) => {
  // Use React.useMemo to ensure the randomization only happens on initial render
  // and doesn't change on every re-render
  const homeTestimonials = React.useMemo(() => {
    // First, sort testimonials by value for potential customers and SEO
    // Prioritize: 1) Comprehensive reviews about major projects 2) Reviews mentioning specific services 3) Recent reviews
    const sortedTestimonials = [...testimonials].sort((a, b) => {
      // First prioritize by text length (more comprehensive reviews)
      const textLengthDiff = b.text.length - a.text.length;
      if (textLengthDiff > 100) return 1; // Significantly longer reviews get priority
      if (textLengthDiff < -100) return -1;

      // Then prioritize by project type (specific project types are more valuable for SEO)
      const aHasProjectType = a.projectType && a.projectType.length > 5;
      const bHasProjectType = b.projectType && b.projectType.length > 5;
      if (aHasProjectType && !bHasProjectType) return -1;
      if (!aHasProjectType && bHasProjectType) return 1;

      // Then prioritize by recency (if dates are available)
      if (a.date && b.date) {
        return new Date(b.date).getTime() - new Date(a.date).getTime();
      }

      // Finally, prioritize by rating
      return b.rating - a.rating;
    });

    // Get the top 10 testimonials (or all if less than 10)
    const topTestimonials = sortedTestimonials.slice(0, Math.min(10, sortedTestimonials.length));

    // Randomly select 5 testimonials from the top 10
    // This ensures we still show high-quality testimonials but with randomization
    const selectedTestimonials: TestimonialType[] = [];
    const availableTestimonials = [...topTestimonials];

    // Always include at least one of the top 3 testimonials (if available)
    if (availableTestimonials.length >= 3) {
      const topIndex = Math.floor(Math.random() * 3); // Random index from 0-2
      selectedTestimonials.push(availableTestimonials[topIndex]);
      availableTestimonials.splice(topIndex, 1);
    }

    // Randomly select the remaining testimonials
    while (selectedTestimonials.length < 5 && availableTestimonials.length > 0) {
      const randomIndex = Math.floor(Math.random() * availableTestimonials.length);
      selectedTestimonials.push(availableTestimonials[randomIndex]);
      availableTestimonials.splice(randomIndex, 1);
    }

    return selectedTestimonials;
  }, [testimonials]);

  return (
    <section className="py-12 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <h2 className="text-2xl font-semibold mb-3 text-center">
          Tilbakemeldinger fra{" "}
          <span className="text-green-500">lokale kunder</span>
        </h2>

        <div className="flex justify-center mb-4">
          <AverageRating testimonials={testimonials} />
        </div>

        <p className="text-gray-600 text-sm text-center mb-8 max-w-2xl mx-auto">
          Vi er stolte av å ha hjulpet mange fornøyde kunder i
          Ringerike-regionen
        </p>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {/* Display the first 5 testimonials */}
          {homeTestimonials.map((testimonial, index) => (
            <div key={index} className="w-full">
              <Testimonial testimonial={testimonial} variant="compact" />
            </div>
          ))}

          {/* Add the "plus" button as the sixth item */}
          <div className="w-full">
            <Link to="/kundehistorier" className="block">
              <div className="p-4 bg-white rounded-lg shadow-sm flex flex-col items-center justify-center hover:bg-gray-50 transition-colors">
                <div className="w-12 h-12 rounded-full bg-green-100 flex items-center justify-center mb-3">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                </div>
                <p className="font-medium text-sm text-green-600">Se alle kundehistorier</p>
              </div>
            </Link>
          </div>
        </div>
      </div>
    </section>
    </>
  );
};

export default TestimonialsPage;
